#!/usr/bin/env python3
"""
效果系統演示腳本

展示TargetSelector和EffectApplier的功能
"""
import sys
import os

# 添加項目根目錄到路徑
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from rpg_system.battle_system.handlers.target_selector import TargetSelector
from rpg_system.battle_system.handlers.effect_applier import EffectApplier
from rpg_system.battle_system.models.combatant import Combatant, CombatantStats
from rpg_system.battle_system.models.battle import Battle
from rpg_system.battle_system.models.skill_instance import SkillInstance
from rpg_system.formula_engine.evaluator import FormulaEvaluator
from rpg_system.config.loader import Config<PERSON>oader


def create_mock_combatant(combatant_id: str, is_player: bool, hp: int, mp: int, level: int) -> Combatant:
    """創建模擬戰鬥者"""
    combatant = Combatant(
        combatant_id=combatant_id,
        card_id="test_card",
        rpg_level=level,
        star_level=3,
        is_player=is_player
    )

    # 設置基礎屬性
    base_stats = CombatantStats(
        hp=hp,
        max_mp=mp,
        mp_regen_per_turn=5,
        patk=100,
        pdef=50,
        matk=80,
        mdef=40,
        spd=70,
        crit_rate=0.1,
        crit_dmg_multiplier=1.5,
        accuracy=0.95,
        evasion=0.05
    )

    combatant.base_stats = base_stats
    combatant.current_hp = hp
    combatant.current_mp = mp

    return combatant


def demo_target_selector():
    """演示目標選擇器功能"""
    print("=== 目標選擇器演示 ===")
    
    # 創建戰鬥者
    player1 = create_mock_combatant("player1", True, 100, 50, 10)
    player2 = create_mock_combatant("player2", True, 80, 40, 8)
    enemy1 = create_mock_combatant("enemy1", False, 120, 60, 12)
    enemy2 = create_mock_combatant("enemy2", False, 90, 30, 9)
    
    # 創建戰鬥上下文
    battle = Battle()
    battle.add_combatant(player1)
    battle.add_combatant(player2)
    battle.add_combatant(enemy1)
    battle.add_combatant(enemy2)
    
    # 創建目標選擇器和依賴
    selector = TargetSelector()
    evaluator = FormulaEvaluator()
    config_loader = ConfigLoader()
    
    # 測試1: 選擇所有敵人
    print("\n1. 選擇所有敵人:")
    target_logic = {"base_pool": "ENEMIES"}
    targets = selector.select_targets(player1, target_logic, battle, evaluator, config_loader)
    print(f"   選中目標: {[t.combatant_id for t in targets]}")
    
    # 測試2: 選擇血量最低的敵人
    print("\n2. 選擇血量最低的敵人:")
    target_logic = {
        "base_pool": "ENEMIES",
        "sort_by": "current_hp",
        "sort_order": "ASC",
        "count_logic": "FIXED",
        "count": 1,
        "selection_strategy": "FIRST_N"
    }
    targets = selector.select_targets(player1, target_logic, battle, evaluator, config_loader)
    print(f"   選中目標: {[t.combatant_id for t in targets]} (血量: {[t.current_hp for t in targets]})")
    
    # 測試3: 選擇血量低於100的敵人
    print("\n3. 選擇血量低於100的敵人:")
    target_logic = {
        "base_pool": "ENEMIES",
        "conditions": [
            {"formula": "target_current_hp < 100"}
        ]
    }
    targets = selector.select_targets(player1, target_logic, battle, evaluator, config_loader)
    print(f"   選中目標: {[t.combatant_id for t in targets]} (血量: {[t.current_hp for t in targets]})")


def demo_effect_applier():
    """演示效果應用器功能"""
    print("\n\n=== 效果應用器演示 ===")
    
    # 創建戰鬥者
    caster = create_mock_combatant("mage", True, 100, 50, 10)
    target = create_mock_combatant("enemy", False, 120, 60, 8)
    
    # 創建戰鬥上下文
    battle = Battle()
    battle.add_combatant(caster)
    battle.add_combatant(target)
    
    # 創建效果應用器和依賴
    selector = TargetSelector()
    evaluator = FormulaEvaluator()
    config_loader = ConfigLoader()
    applier = EffectApplier(evaluator, selector, config_loader)
    
    print(f"\n施法者: {caster.combatant_id} (HP: {caster.current_hp}, MP: {caster.current_mp})")
    print(f"目標: {target.combatant_id} (HP: {target.current_hp}, MP: {target.current_mp})")
    
    # 測試1: 傷害效果
    print("\n1. 應用傷害效果:")
    damage_effects = [
        {
            "type": "DAMAGE",
            "value_formula": "caster_stat_matk * 1.2",
            "damage_type": "MAGICAL"
        }
    ]
    
    log_entries = applier.apply_effect_definitions(
        caster, [target], damage_effects, battle, ["FIRE", "MAGIC"]
    )
    
    for entry in log_entries:
        if entry.event_type == "DAMAGE":
            print(f"   造成傷害: {entry.damage_dealt}")
            print(f"   目標剩餘HP: {target.current_hp}")
    
    # 測試2: 治療效果
    print("\n2. 應用治療效果:")
    heal_effects = [
        {
            "type": "HEAL",
            "value_formula": "caster_stat_matk * 0.8"
        }
    ]
    
    log_entries = applier.apply_effect_definitions(
        caster, [target], heal_effects, battle, ["HEAL", "MAGIC"]
    )
    
    for entry in log_entries:
        if entry.event_type == "HEALING":
            print(f"   治療量: {entry.healing_done}")
            print(f"   目標當前HP: {target.current_hp}")
    
    # 測試3: MP恢復效果
    print("\n3. 應用MP恢復效果:")
    mp_effects = [
        {
            "type": "GAIN_MP",
            "value_formula": "20"
        }
    ]
    
    log_entries = applier.apply_effect_definitions(
        caster, [target], mp_effects, battle, ["RESTORE"]
    )
    
    for entry in log_entries:
        if entry.event_type == "MP_GAIN":
            print(f"   MP恢復: {abs(entry.mp_consumed)}")
            print(f"   目標當前MP: {target.current_mp}")


def demo_complex_skill():
    """演示複雜技能效果"""
    print("\n\n=== 複雜技能演示 ===")
    
    # 創建戰鬥者
    healer = create_mock_combatant("healer", True, 80, 100, 12)
    ally1 = create_mock_combatant("ally1", True, 30, 20, 8)  # 低血量
    ally2 = create_mock_combatant("ally2", True, 90, 40, 10)  # 高血量
    
    # 創建戰鬥上下文
    battle = Battle()
    battle.add_combatant(healer)
    battle.add_combatant(ally1)
    battle.add_combatant(ally2)
    
    # 創建效果應用器和依賴
    selector = TargetSelector()
    evaluator = FormulaEvaluator()
    config_loader = ConfigLoader()
    applier = EffectApplier(evaluator, selector, config_loader)
    
    print(f"\n治療師: {healer.combatant_id} (HP: {healer.current_hp}, MP: {healer.current_mp})")
    print(f"隊友1: {ally1.combatant_id} (HP: {ally1.current_hp}, MP: {ally1.current_mp})")
    print(f"隊友2: {ally2.combatant_id} (HP: {ally2.current_hp}, MP: {ally2.current_mp})")
    
    # 智能治療技能：優先治療血量低的友方單位
    print("\n智能治療技能 - 優先治療血量最低的友方:")
    smart_heal_effects = [
        {
            "type": "HEAL",
            "value_formula": "caster_stat_matk * 1.5",
            "target_override": {
                "base_pool": "ALLIES",
                "conditions": [
                    {"formula": "target_current_hp_percent < 0.8"}  # 血量低於80%
                ],
                "sort_by": "current_hp",
                "sort_order": "ASC",  # 血量最低的優先
                "count_logic": "FIXED",
                "count": 1,
                "selection_strategy": "FIRST_N"
            }
        }
    ]
    
    log_entries = applier.apply_effect_definitions(
        healer, [ally1, ally2], smart_heal_effects, battle, ["HEAL", "MAGIC"]
    )
    
    for entry in log_entries:
        if entry.event_type == "HEALING":
            target_id = entry.target_ids[0]
            print(f"   治療目標: {target_id}")
            print(f"   治療量: {entry.healing_done}")
    
    print(f"\n治療後狀態:")
    print(f"隊友1: {ally1.combatant_id} (HP: {ally1.current_hp})")
    print(f"隊友2: {ally2.combatant_id} (HP: {ally2.current_hp})")


def main():
    """主函數"""
    print("RPG 效果系統演示")
    print("=" * 50)
    
    try:
        demo_target_selector()
        demo_effect_applier()
        demo_complex_skill()
        
        print("\n\n演示完成！")
        print("效果系統功能包括:")
        print("- 靈活的目標選擇 (基礎池、條件過濾、排序、數量控制)")
        print("- 多種效果類型 (傷害、治療、MP操作)")
        print("- 公式驅動的數值計算")
        print("- 完整的戰鬥日誌記錄")
        
    except Exception as e:
        print(f"演示過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
